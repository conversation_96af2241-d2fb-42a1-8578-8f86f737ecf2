/**
 * Sync Status Manager - Phase 3 Implementation
 * 
 * Enterprise-grade sync status management with:
 * - Real-time operation tracking
 * - Connection status monitoring
 * - Performance metrics
 * - Status persistence and recovery
 * - WebSocket broadcasting
 * 
 * <AUTHOR> Project Team
 * @version 3.0.0
 */

import { createClient } from '@/utils/supabase/client'
import type { RealtimeChannel } from '@supabase/supabase-js'

// Types
export interface SyncOperation {
  id: string
  operation_type: 'upload' | 'delete' | 'update' | 'full_sync' | 'webhook'
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
  progress: number
  total_items: number
  processed_items: number
  failed_items: number
  start_time: string
  end_time: string | null
  estimated_completion: string | null
  triggered_by: string | null
  source: 'manual' | 'webhook' | 'api' | 'scheduled'
  operation_data: Record<string, any>
  error_details: Record<string, any>
  performance_metrics: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ConnectionStatus {
  id: string
  client_id: string
  connection_type: 'websocket' | 'sse' | 'polling'
  status: 'connected' | 'disconnected' | 'reconnecting' | 'error'
  last_ping: string
  connection_start: string
  disconnect_reason: string | null
  user_agent: string | null
  ip_address: string | null
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface SyncStatusSnapshot {
  id: string
  snapshot_type: 'hourly' | 'daily' | 'manual' | 'error'
  total_assets: number
  synced_assets: number
  pending_assets: number
  error_assets: number
  active_operations: number
  last_sync_time: string | null
  system_health: 'healthy' | 'warning' | 'critical'
  performance_score: number
  error_rate: number
  avg_sync_time_ms: number
  metadata: Record<string, any>
  created_at: string
}

export interface SyncStatusUpdate {
  operation_id: string
  operation_type: string
  status: string
  progress: number
  message?: string
  timestamp: string
  performance_data?: Record<string, any>
}

export class SyncStatusManager {
  private static supabase = createClient()
  private static broadcastChannel: RealtimeChannel | null = null
  private static statusSubscribers: Set<(update: SyncStatusUpdate) => void> = new Set()
  private static schemaReady: boolean | null = null

  /**
   * Check if Phase 3 schema is available
   */
  private static async checkSchemaAvailability(): Promise<boolean> {
    if (this.schemaReady !== null) {
      return this.schemaReady
    }

    try {
      // Test if sync_operations table exists by attempting a simple query
      const { error } = await this.supabase
        .from('sync_operations')
        .select('id')
        .limit(1)

      this.schemaReady = !error

      if (!this.schemaReady) {
        console.warn('[SyncStatusManager] Phase 3 schema not available - running in compatibility mode')
        console.warn('[SyncStatusManager] Please run the database migration: supabase-schema.sql')
      }

      return this.schemaReady
    } catch (error) {
      console.warn('[SyncStatusManager] Schema check failed - Phase 3 features disabled')
      this.schemaReady = false
      return false
    }
  }

  /**
   * Initialize real-time broadcasting
   */
  static async initializeBroadcasting(): Promise<void> {
    try {
      const schemaAvailable = await this.checkSchemaAvailability()

      if (!schemaAvailable) {
        console.warn('[SyncStatusManager] Broadcasting disabled - Phase 3 schema not available')
        return
      }

      if (this.broadcastChannel) {
        return // Already initialized
      }

      console.log('[SyncStatusManager] Initializing real-time broadcasting...')

      this.broadcastChannel = this.supabase
        .channel('sync_status_broadcast')
        .on('broadcast', { event: 'sync_update' }, (payload) => {
          console.log('[SyncStatusManager] Received broadcast:', payload)
          this.notifySubscribers(payload.payload as SyncStatusUpdate)
        })
        .subscribe((status) => {
          console.log('[SyncStatusManager] Broadcast channel status:', status)
        })
    } catch (error) {
      console.error('[SyncStatusManager] Failed to initialize broadcasting:', error)
      // Don't throw - allow app to continue without real-time features
    }
  }

  /**
   * Subscribe to sync status updates
   */
  static subscribe(callback: (update: SyncStatusUpdate) => void): () => void {
    this.statusSubscribers.add(callback)
    
    // Initialize broadcasting if not already done
    this.initializeBroadcasting()
    
    return () => {
      this.statusSubscribers.delete(callback)
    }
  }

  /**
   * Notify all subscribers of status update
   */
  private static notifySubscribers(update: SyncStatusUpdate): void {
    this.statusSubscribers.forEach(callback => {
      try {
        callback(update)
      } catch (error) {
        console.error('[SyncStatusManager] Subscriber callback error:', error)
      }
    })
  }

  /**
   * Create new sync operation
   */
  static async createSyncOperation(params: {
    operation_type: SyncOperation['operation_type']
    total_items?: number
    triggered_by?: string
    source?: SyncOperation['source']
    operation_data?: Record<string, any>
  }): Promise<string> {
    try {
      const schemaAvailable = await this.checkSchemaAvailability()

      if (!schemaAvailable) {
        // Generate a mock operation ID for compatibility
        const mockId = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        console.warn('[SyncStatusManager] Schema not available - returning mock operation ID:', mockId)
        return mockId
      }

      const { data, error } = await this.supabase
        .from('sync_operations')
        .insert({
          operation_type: params.operation_type,
          total_items: params.total_items || 0,
          triggered_by: params.triggered_by,
          source: params.source || 'manual',
          operation_data: params.operation_data || {}
        })
        .select('id')
        .single()

      if (error) {
        throw new Error(`Failed to create sync operation: ${error.message}`)
      }

      const operationId = data.id
      console.log('[SyncStatusManager] Created sync operation:', operationId)

      // Broadcast creation
      await this.broadcastSyncUpdate({
        operation_id: operationId,
        operation_type: params.operation_type,
        status: 'pending',
        progress: 0,
        message: 'Sync operation created',
        timestamp: new Date().toISOString()
      })

      return operationId
    } catch (error) {
      console.error('[SyncStatusManager] Failed to create sync operation:', error)
      // Return mock ID instead of throwing to prevent app crashes
      const mockId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      return mockId
    }
  }

  /**
   * Update sync operation progress
   */
  static async updateSyncProgress(
    operationId: string,
    progress: number,
    processedItems: number = 0,
    failedItems: number = 0,
    message?: string
  ): Promise<void> {
    try {
      const schemaAvailable = await this.checkSchemaAvailability()

      if (!schemaAvailable) {
        console.warn('[SyncStatusManager] Schema not available - skipping progress update for:', operationId)
        return
      }

      const { error } = await this.supabase
        .rpc('update_sync_operation_progress', {
          operation_id_param: operationId,
          progress_param: progress,
          processed_items_param: processedItems,
          failed_items_param: failedItems,
          message_param: message
        })

      if (error) {
        throw new Error(`Failed to update sync progress: ${error.message}`)
      }

      // Get updated operation for broadcasting
      const { data: operation } = await this.supabase
        .from('sync_operations')
        .select('operation_type, status')
        .eq('id', operationId)
        .single()

      // Broadcast progress update
      await this.broadcastSyncUpdate({
        operation_id: operationId,
        operation_type: operation?.operation_type || 'unknown',
        status: 'in_progress',
        progress,
        message: message || `Progress: ${progress}%`,
        timestamp: new Date().toISOString(),
        performance_data: {
          processed_items: processedItems,
          failed_items: failedItems
        }
      })

      console.log(`[SyncStatusManager] Updated progress for ${operationId}: ${progress}%`)
    } catch (error) {
      console.error('[SyncStatusManager] Failed to update sync progress:', error)
      // Don't throw - allow sync to continue even if progress tracking fails
    }
  }

  /**
   * Complete sync operation
   */
  static async completeSyncOperation(
    operationId: string,
    status: 'completed' | 'failed' | 'cancelled',
    errorDetails?: Record<string, any>,
    message?: string
  ): Promise<void> {
    try {
      const schemaAvailable = await this.checkSchemaAvailability()

      if (!schemaAvailable) {
        console.warn('[SyncStatusManager] Schema not available - skipping completion for:', operationId)
        return
      }

      const { error } = await this.supabase
        .rpc('complete_sync_operation', {
          operation_id_param: operationId,
          status_param: status,
          error_details_param: errorDetails,
          message_param: message
        })

      if (error) {
        throw new Error(`Failed to complete sync operation: ${error.message}`)
      }

      // Get operation details for broadcasting
      const { data: operation } = await this.supabase
        .from('sync_operations')
        .select('operation_type, progress')
        .eq('id', operationId)
        .single()

      // Broadcast completion
      await this.broadcastSyncUpdate({
        operation_id: operationId,
        operation_type: operation?.operation_type || 'unknown',
        status,
        progress: operation?.progress || 100,
        message: message || `Operation ${status}`,
        timestamp: new Date().toISOString(),
        performance_data: errorDetails
      })

      console.log(`[SyncStatusManager] Completed sync operation ${operationId}: ${status}`)
    } catch (error) {
      console.error('[SyncStatusManager] Failed to complete sync operation:', error)
      // Don't throw - allow sync to continue even if completion tracking fails
    }
  }

  /**
   * Broadcast sync status update
   */
  static async broadcastSyncUpdate(update: SyncStatusUpdate): Promise<void> {
    try {
      if (!this.broadcastChannel) {
        await this.initializeBroadcasting()
      }

      await this.broadcastChannel?.send({
        type: 'broadcast',
        event: 'sync_update',
        payload: update
      })

      // Also notify local subscribers
      this.notifySubscribers(update)
    } catch (error) {
      console.error('[SyncStatusManager] Failed to broadcast sync update:', error)
    }
  }

  /**
   * Get active sync operations
   */
  static async getActiveSyncOperations(): Promise<SyncOperation[]> {
    try {
      const schemaAvailable = await this.checkSchemaAvailability()

      if (!schemaAvailable) {
        console.warn('[SyncStatusManager] Schema not available - returning empty operations list')
        return []
      }

      const { data, error } = await this.supabase
        .from('sync_operations')
        .select('*')
        .in('status', ['pending', 'in_progress'])
        .order('created_at', { ascending: false })

      if (error) {
        throw new Error(`Failed to get active sync operations: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('[SyncStatusManager] Failed to get active sync operations:', error)
      return []
    }
  }

  /**
   * Get sync operation by ID
   */
  static async getSyncOperation(operationId: string): Promise<SyncOperation | null> {
    try {
      const { data, error } = await this.supabase
        .from('sync_operations')
        .select('*')
        .eq('id', operationId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          return null // Not found
        }
        throw new Error(`Failed to get sync operation: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('[SyncStatusManager] Failed to get sync operation:', error)
      return null
    }
  }

  /**
   * Create status snapshot
   */
  static async createStatusSnapshot(type: SyncStatusSnapshot['snapshot_type'] = 'manual'): Promise<string> {
    try {
      const { data, error } = await this.supabase
        .rpc('create_sync_status_snapshot', {
          snapshot_type_param: type
        })

      if (error) {
        throw new Error(`Failed to create status snapshot: ${error.message}`)
      }

      console.log('[SyncStatusManager] Created status snapshot:', data)
      return data
    } catch (error) {
      console.error('[SyncStatusManager] Failed to create status snapshot:', error)
      throw error
    }
  }

  /**
   * Get recent status snapshots
   */
  static async getRecentSnapshots(limit: number = 10): Promise<SyncStatusSnapshot[]> {
    try {
      const schemaAvailable = await this.checkSchemaAvailability()

      if (!schemaAvailable) {
        console.warn('[SyncStatusManager] Schema not available - returning empty snapshots list')
        return []
      }

      const { data, error } = await this.supabase
        .from('sync_status_snapshots')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        throw new Error(`Failed to get status snapshots: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('[SyncStatusManager] Failed to get status snapshots:', error)
      return []
    }
  }

  /**
   * Cleanup old operations and snapshots
   */
  static async cleanup(daysToKeep: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

      // Clean up old completed operations
      const { error: opsError } = await this.supabase
        .from('sync_operations')
        .delete()
        .in('status', ['completed', 'failed', 'cancelled'])
        .lt('created_at', cutoffDate.toISOString())

      if (opsError) {
        console.warn('[SyncStatusManager] Failed to cleanup old operations:', opsError)
      }

      // Clean up old snapshots
      const { error: snapshotsError } = await this.supabase
        .from('sync_status_snapshots')
        .delete()
        .lt('created_at', cutoffDate.toISOString())

      if (snapshotsError) {
        console.warn('[SyncStatusManager] Failed to cleanup old snapshots:', snapshotsError)
      }

      console.log('[SyncStatusManager] Cleanup completed')
    } catch (error) {
      console.error('[SyncStatusManager] Cleanup failed:', error)
    }
  }

  /**
   * Cleanup resources
   */
  static cleanupResources(): void {
    if (this.broadcastChannel) {
      this.supabase.removeChannel(this.broadcastChannel)
      this.broadcastChannel = null
    }
    this.statusSubscribers.clear()
  }
}
