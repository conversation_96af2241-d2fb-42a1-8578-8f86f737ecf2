-- Phase 3 Database Verification Script
-- Run this in Supabase SQL Editor to verify Phase 3 setup

-- 1. Check if all Phase 3 tables exist
SELECT 
    'Tables Check' as check_type,
    CASE 
        WHEN COUNT(*) = 3 THEN '✅ All Phase 3 tables exist'
        ELSE '❌ Missing tables: ' || (3 - COUNT(*))::text
    END as status,
    string_agg(table_name, ', ') as found_tables
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('sync_operations', 'connection_status', 'sync_status_snapshots');

-- 2. Check if all Phase 3 functions exist
SELECT 
    'Functions Check' as check_type,
    CASE 
        WHEN COUNT(*) = 3 THEN '✅ All Phase 3 functions exist'
        ELSE '❌ Missing functions: ' || (3 - COUNT(*))::text
    END as status,
    string_agg(routine_name, ', ') as found_functions
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('update_sync_operation_progress', 'complete_sync_operation', 'create_sync_status_snapshot');

-- 3. Check if triggers exist
SELECT 
    'Triggers Check' as check_type,
    CASE 
        WHEN COUNT(*) >= 1 THEN '✅ Phase 3 triggers exist'
        ELSE '❌ Missing triggers'
    END as status,
    string_agg(trigger_name, ', ') as found_triggers
FROM information_schema.triggers 
WHERE event_object_schema = 'public' 
AND trigger_name LIKE '%sync%';

-- 4. Test sync_operations table structure
SELECT 
    'sync_operations Structure' as check_type,
    CASE 
        WHEN COUNT(*) >= 15 THEN '✅ sync_operations has all required columns'
        ELSE '❌ sync_operations missing columns'
    END as status,
    COUNT(*)::text || ' columns found' as details
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'sync_operations';

-- 5. Test creating a sample sync operation
DO $$
DECLARE
    test_id UUID;
BEGIN
    -- Try to insert a test operation
    INSERT INTO sync_operations (operation_type, source, total_items) 
    VALUES ('upload', 'manual', 1) 
    RETURNING id INTO test_id;
    
    -- Clean up the test record
    DELETE FROM sync_operations WHERE id = test_id;
    
    -- Report success
    RAISE NOTICE '✅ sync_operations table is functional - test insert/delete successful';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ sync_operations table error: %', SQLERRM;
END $$;

-- 6. Test function calls
DO $$
DECLARE
    test_id UUID;
    func_result BOOLEAN;
BEGIN
    -- Create test operation
    INSERT INTO sync_operations (operation_type, source) 
    VALUES ('upload', 'manual') 
    RETURNING id INTO test_id;
    
    -- Test progress update function
    SELECT update_sync_operation_progress(test_id, 50, 5, 0, 'Test progress') INTO func_result;
    
    -- Test completion function
    SELECT complete_sync_operation(test_id, 'completed', NULL, 'Test completion') INTO func_result;
    
    -- Clean up
    DELETE FROM sync_operations WHERE id = test_id;
    
    RAISE NOTICE '✅ Phase 3 functions are working correctly';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Phase 3 functions error: %', SQLERRM;
        -- Clean up on error
        DELETE FROM sync_operations WHERE operation_type = 'upload' AND source = 'manual';
END $$;

-- 7. Test snapshot creation
DO $$
DECLARE
    snapshot_id UUID;
BEGIN
    -- Test snapshot function
    SELECT create_sync_status_snapshot('manual') INTO snapshot_id;
    
    -- Clean up
    DELETE FROM sync_status_snapshots WHERE id = snapshot_id;
    
    RAISE NOTICE '✅ Snapshot creation function is working correctly';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Snapshot function error: %', SQLERRM;
END $$;

-- 8. Final summary
SELECT 
    '🎉 PHASE 3 VERIFICATION COMPLETE' as summary,
    'Check the messages above for any ❌ errors' as instructions,
    'If all checks show ✅, your Phase 3 database is ready!' as status;
