/**
 * Database Status Banner
 * 
 * Shows the current database schema status and provides guidance for setup.
 * Only appears when Phase 3 schema is not available.
 * 
 * <AUTHOR> Project Team
 * @version 3.0.0
 */

'use client'

import React, { useState, useEffect } from 'react'
import { AlertTriangle, CheckCircle, Database, ExternalLink, RefreshCw, X } from 'lucide-react'
import { createClient } from '@/utils/supabase/client'

interface DatabaseStatusBannerProps {
  className?: string
}

export function DatabaseStatusBanner({ className = '' }: DatabaseStatusBannerProps) {
  const [schemaReady, setSchemaReady] = useState<boolean | null>(null)
  const [isChecking, setIsChecking] = useState(true)
  const [isDismissed, setIsDismissed] = useState(false)

  const checkSchemaStatus = async () => {
    setIsChecking(true)
    try {
      const supabase = createClient()
      
      // Test if Phase 3 tables exist
      const { error } = await supabase
        .from('sync_operations')
        .select('id')
        .limit(1)

      setSchemaReady(!error)
    } catch (error) {
      setSchemaReady(false)
    } finally {
      setIsChecking(false)
    }
  }

  useEffect(() => {
    checkSchemaStatus()
  }, [])

  // Don't show if schema is ready or banner is dismissed
  if (schemaReady || isDismissed) {
    return null
  }

  // Don't show while checking
  if (isChecking) {
    return null
  }

  return (
    <div className={`bg-amber-50 border-l-4 border-amber-400 p-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-amber-400" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-amber-800">
            Phase 3 Database Schema Required
          </h3>
          <div className="mt-2 text-sm text-amber-700">
            <p>
              Your application is running in compatibility mode. To enable enterprise-grade sync status management, 
              please update your database schema.
            </p>
          </div>
          <div className="mt-4">
            <div className="flex space-x-3">
              <button
                onClick={checkSchemaStatus}
                disabled={isChecking}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-amber-800 bg-amber-100 hover:bg-amber-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isChecking ? 'animate-spin' : ''}`} />
                {isChecking ? 'Checking...' : 'Recheck Status'}
              </button>
              
              <a
                href="https://supabase.com"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-amber-800 bg-amber-100 hover:bg-amber-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500"
              >
                <Database className="h-4 w-4 mr-2" />
                Open Supabase
                <ExternalLink className="h-4 w-4 ml-1" />
              </a>
            </div>
          </div>
          <div className="mt-3 text-xs text-amber-600 bg-amber-100 p-3 rounded">
            <strong>Quick Setup:</strong>
            <ol className="list-decimal list-inside mt-1 space-y-1">
              <li>Open your Supabase dashboard</li>
              <li>Go to SQL Editor</li>
              <li>Copy and run the complete schema from <code>docs/full-complete-supabase-script.md</code></li>
              <li>Refresh this page</li>
            </ol>
          </div>
        </div>
        <div className="ml-auto pl-3">
          <div className="-mx-1.5 -my-1.5">
            <button
              onClick={() => setIsDismissed(true)}
              className="inline-flex rounded-md p-1.5 text-amber-500 hover:bg-amber-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-amber-50 focus:ring-amber-600"
            >
              <span className="sr-only">Dismiss</span>
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DatabaseStatusBanner
