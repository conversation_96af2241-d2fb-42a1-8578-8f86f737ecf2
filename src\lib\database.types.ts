export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: number
          email: string
          name: string
          password: string
          phone: string | null
          address: string | null
          role: string
          status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          email: string
          name: string
          password: string
          phone?: string | null
          address?: string | null
          role?: string
          status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          email?: string
          name?: string
          password?: string
          phone?: string | null
          address?: string | null
          role?: string
          status?: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED'
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      sync_operations: {
        Row: {
          id: string
          operation_type: 'upload' | 'delete' | 'update' | 'full_sync' | 'webhook'
          status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
          progress: number
          total_items: number
          processed_items: number
          failed_items: number
          start_time: string
          end_time: string | null
          estimated_completion: string | null
          triggered_by: string | null
          source: 'manual' | 'webhook' | 'api' | 'scheduled'
          operation_data: Record<string, any>
          error_details: Record<string, any>
          performance_metrics: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          operation_type: 'upload' | 'delete' | 'update' | 'full_sync' | 'webhook'
          status?: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
          progress?: number
          total_items?: number
          processed_items?: number
          failed_items?: number
          start_time?: string
          end_time?: string | null
          estimated_completion?: string | null
          triggered_by?: string | null
          source?: 'manual' | 'webhook' | 'api' | 'scheduled'
          operation_data?: Record<string, any>
          error_details?: Record<string, any>
          performance_metrics?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          operation_type?: 'upload' | 'delete' | 'update' | 'full_sync' | 'webhook'
          status?: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
          progress?: number
          total_items?: number
          processed_items?: number
          failed_items?: number
          start_time?: string
          end_time?: string | null
          estimated_completion?: string | null
          triggered_by?: string | null
          source?: 'manual' | 'webhook' | 'api' | 'scheduled'
          operation_data?: Record<string, any>
          error_details?: Record<string, any>
          performance_metrics?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      connection_status: {
        Row: {
          id: string
          client_id: string
          connection_type: 'websocket' | 'sse' | 'polling'
          status: 'connected' | 'disconnected' | 'reconnecting' | 'error'
          last_ping: string
          connection_start: string
          disconnect_reason: string | null
          user_agent: string | null
          ip_address: string | null
          metadata: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          client_id: string
          connection_type: 'websocket' | 'sse' | 'polling'
          status?: 'connected' | 'disconnected' | 'reconnecting' | 'error'
          last_ping?: string
          connection_start?: string
          disconnect_reason?: string | null
          user_agent?: string | null
          ip_address?: string | null
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          client_id?: string
          connection_type?: 'websocket' | 'sse' | 'polling'
          status?: 'connected' | 'disconnected' | 'reconnecting' | 'error'
          last_ping?: string
          connection_start?: string
          disconnect_reason?: string | null
          user_agent?: string | null
          ip_address?: string | null
          metadata?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      sync_status_snapshots: {
        Row: {
          id: string
          snapshot_type: 'hourly' | 'daily' | 'manual' | 'error'
          total_assets: number
          synced_assets: number
          pending_assets: number
          error_assets: number
          active_operations: number
          last_sync_time: string | null
          system_health: 'healthy' | 'warning' | 'critical'
          performance_score: number
          error_rate: number
          avg_sync_time_ms: number
          metadata: Record<string, any>
          created_at: string
        }
        Insert: {
          id?: string
          snapshot_type: 'hourly' | 'daily' | 'manual' | 'error'
          total_assets?: number
          synced_assets?: number
          pending_assets?: number
          error_assets?: number
          active_operations?: number
          last_sync_time?: string | null
          system_health?: 'healthy' | 'warning' | 'critical'
          performance_score?: number
          error_rate?: number
          avg_sync_time_ms?: number
          metadata?: Record<string, any>
          created_at?: string
        }
        Update: {
          id?: string
          snapshot_type?: 'hourly' | 'daily' | 'manual' | 'error'
          total_assets?: number
          synced_assets?: number
          pending_assets?: number
          error_assets?: number
          active_operations?: number
          last_sync_time?: string | null
          system_health?: 'healthy' | 'warning' | 'critical'
          performance_score?: number
          error_rate?: number
          avg_sync_time_ms?: number
          metadata?: Record<string, any>
          created_at?: string
        }
        Relationships: []
      }
      personnel: {
        Row: {
          id: number
          name: string
          email: string
          phone: string | null
          address: string | null
          profile_photo: string | null
          department: string
          position: string | null
          hire_date: string | null
          status: 'Active' | 'Inactive' | 'On Leave' | 'Suspended'
          biography: string | null
          spouse_name: string | null
          spouse_occupation: string | null
          children_count: string | null
          emergency_contact: string | null
          children_names: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          name: string
          email: string
          phone?: string | null
          address?: string | null
          profile_photo?: string | null
          department: string
          position?: string | null
          hire_date?: string | null
          status?: 'Active' | 'Inactive' | 'On Leave' | 'Suspended'
          biography?: string | null
          spouse_name?: string | null
          spouse_occupation?: string | null
          children_count?: string | null
          emergency_contact?: string | null
          children_names?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          name?: string
          email?: string
          phone?: string | null
          address?: string | null
          profile_photo?: string | null
          department?: string
          position?: string | null
          hire_date?: string | null
          status?: 'Active' | 'Inactive' | 'On Leave' | 'Suspended'
          biography?: string | null
          spouse_name?: string | null
          spouse_occupation?: string | null
          children_count?: string | null
          emergency_contact?: string | null
          children_names?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      personnel_documents: {
        Row: {
          id: number
          filename: string
          original_name: string
          mime_type: string
          size: number
          path: string
          personnel_id: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: number
          filename: string
          original_name: string
          mime_type: string
          size: number
          path: string
          personnel_id: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: number
          filename?: string
          original_name?: string
          mime_type?: string
          size?: number
          path?: string
          personnel_id?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "personnel_documents_personnel_id_fkey"
            columns: ["personnel_id"]
            isOneToOne: false
            referencedRelation: "personnel"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      update_sync_operation_progress: {
        Args: {
          operation_id_param: string
          progress_param: number
          processed_items_param: number
          failed_items_param: number
          message_param?: string
        }
        Returns: void
      }
      complete_sync_operation: {
        Args: {
          operation_id_param: string
          status_param: 'completed' | 'failed' | 'cancelled'
          error_details_param?: Record<string, any>
          message_param?: string
        }
        Returns: void
      }
      create_sync_status_snapshot: {
        Args: {
          snapshot_type_param: 'hourly' | 'daily' | 'manual' | 'error'
        }
        Returns: string
      }
    }
    Enums: {
      user_status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED'
      personnel_status: 'Active' | 'Inactive' | 'On Leave' | 'Suspended'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Type helpers for easier usage
export type User = Database['public']['Tables']['users']['Row']
export type Personnel = Database['public']['Tables']['personnel']['Row']
export type PersonnelDocument = Database['public']['Tables']['personnel_documents']['Row']
export type SyncOperation = Database['public']['Tables']['sync_operations']['Row']
export type ConnectionStatus = Database['public']['Tables']['connection_status']['Row']
export type SyncStatusSnapshot = Database['public']['Tables']['sync_status_snapshots']['Row']

export type UserInsert = Database['public']['Tables']['users']['Insert']
export type PersonnelInsert = Database['public']['Tables']['personnel']['Insert']
export type PersonnelDocumentInsert = Database['public']['Tables']['personnel_documents']['Insert']
export type SyncOperationInsert = Database['public']['Tables']['sync_operations']['Insert']
export type ConnectionStatusInsert = Database['public']['Tables']['connection_status']['Insert']
export type SyncStatusSnapshotInsert = Database['public']['Tables']['sync_status_snapshots']['Insert']

export type UserUpdate = Database['public']['Tables']['users']['Update']
export type PersonnelUpdate = Database['public']['Tables']['personnel']['Update']
export type PersonnelDocumentUpdate = Database['public']['Tables']['personnel_documents']['Update']
