/**
 * Conditional Sync Status Provider
 * 
 * This provider only loads the SyncStatusProvider if the Phase 3 database schema is available.
 * This prevents the application from hanging when the sync_operations tables don't exist.
 * 
 * <AUTHOR> Project Team
 * @version 3.0.0
 */

'use client'

import React, { useState, useEffect, createContext, useContext } from 'react'
import SyncStatusProvider from './SyncStatusProvider'
import { createClient } from '@/utils/supabase/client'

// Fallback context for when sync status is not available
const FallbackSyncStatusContext = createContext({
  isConnected: false,
  activeOperationsCount: 0,
  systemHealth: { status: 'healthy' as const, score: 100 },
  lastUpdate: null,
  connection: { status: 'disconnected' as const, latency: 0, reconnectAttempts: 0 },
  metrics: { totalOperations: 0, successRate: 100, avgDuration: 0, peakConcurrency: 0 },
  isHealthy: true
})

export const useSyncStatus = () => {
  const context = useContext(FallbackSyncStatusContext)
  return context
}

interface ConditionalSyncStatusProviderProps {
  children: React.ReactNode
}

export function ConditionalSyncStatusProvider({ children }: ConditionalSyncStatusProviderProps) {
  const [schemaReady, setSchemaReady] = useState<boolean | null>(null)
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkSchemaAvailability = async () => {
      try {
        const supabase = createClient()
        
        // Quick test to see if sync_operations table exists
        const { error } = await supabase
          .from('sync_operations')
          .select('id')
          .limit(1)

        const isReady = !error
        setSchemaReady(isReady)
        
        if (isReady) {
          console.log('✅ [ConditionalSyncStatusProvider] Phase 3 schema detected - enabling sync status features')
        } else {
          console.warn('⚠️ [ConditionalSyncStatusProvider] Phase 3 schema not available - running without sync status features')
          console.warn('💡 [ConditionalSyncStatusProvider] To enable sync status features, run the complete database schema from docs/full-complete-supabase-script.md')
        }
      } catch (error) {
        console.warn('[ConditionalSyncStatusProvider] Schema check failed:', error)
        setSchemaReady(false)
      } finally {
        setIsChecking(false)
      }
    }

    checkSchemaAvailability()
  }, [])

  // Show loading state while checking
  if (isChecking) {
    return <>{children}</>
  }

  // If schema is ready, use the full SyncStatusProvider
  if (schemaReady) {
    return (
      <SyncStatusProvider>
        {children}
      </SyncStatusProvider>
    )
  }

  // If schema is not ready, provide fallback context
  return (
    <FallbackSyncStatusContext.Provider value={{
      isConnected: false,
      activeOperationsCount: 0,
      systemHealth: { status: 'healthy', score: 100 },
      lastUpdate: null,
      connection: { status: 'disconnected', latency: 0, reconnectAttempts: 0 },
      metrics: { totalOperations: 0, successRate: 100, avgDuration: 0, peakConcurrency: 0 },
      isHealthy: true
    }}>
      {children}
    </FallbackSyncStatusContext.Provider>
  )
}

export default ConditionalSyncStatusProvider
