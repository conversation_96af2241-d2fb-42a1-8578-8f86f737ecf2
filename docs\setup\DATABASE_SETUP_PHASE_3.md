# 🚀 Phase 3 Database Setup Guide

## ⚠️ **CRITICAL: Database Schema Update Required**

You're seeing the error `relation "public.sync_operations" does not exist` because the Phase 3 tables haven't been created in your Supabase database yet.

## 📋 **Quick Setup Steps**

### **Step 1: Open Supabase Dashboard**
1. Go to [supabase.com](https://supabase.com)
2. Sign in to your account
3. Select your LGU project
4. Navigate to **SQL Editor** in the left sidebar

### **Step 2: Execute Phase 3 Schema**
1. Click **"New Query"** in the SQL Editor
2. Copy the **complete schema** from `supabase-schema.sql` in your project root
3. Paste it into the SQL Editor
4. Click **"Run"** to execute

### **Step 3: Verify Installation**
After running the schema, you should see these new tables in your **Table Editor**:
- ✅ `sync_operations` - Real-time operation tracking
- ✅ `connection_status` - WebSocket connection monitoring  
- ✅ `sync_status_snapshots` - System health snapshots

## 🔧 **What Gets Created**

### **Tables**
```sql
-- Real-time sync operation tracking
sync_operations (
  id, operation_type, status, progress, total_items,
  processed_items, failed_items, start_time, end_time,
  triggered_by, source, operation_data, error_details,
  performance_metrics, created_at, updated_at
)

-- WebSocket connection monitoring
connection_status (
  id, client_id, connection_type, status, last_ping,
  connection_start, disconnect_reason, user_agent,
  ip_address, metadata, created_at, updated_at
)

-- System health snapshots
sync_status_snapshots (
  id, snapshot_type, total_assets, synced_assets,
  pending_assets, error_assets, active_operations,
  last_sync_time, system_health, performance_score,
  error_rate, avg_sync_time_ms, metadata, created_at
)
```

### **Functions**
```sql
-- Progress tracking
update_sync_operation_progress(operation_id, progress, processed, failed, message)

-- Operation completion
complete_sync_operation(operation_id, status, error_details, message)

-- Health snapshots
create_sync_status_snapshot(snapshot_type)
```

### **Triggers**
```sql
-- Real-time notifications
sync_operations_notify_trigger → notify_sync_status_change()
```

## 🎯 **After Setup**

Once the schema is installed:

1. **Refresh your media page** - The error should disappear
2. **Test sync operations** - Upload a file to see real-time tracking
3. **Check sync indicators** - You'll see live status updates
4. **Monitor system health** - Dashboard shows performance metrics

## 🔍 **Troubleshooting**

### **Error: "permission denied"**
- Make sure you're the project owner or have admin access
- Check that RLS policies allow your operations

### **Error: "function does not exist"**
- Ensure all functions were created successfully
- Re-run the complete schema if needed

### **Error: "table already exists"**
- If you get this error, the tables are already created
- Check the **Table Editor** to verify they exist

## 📊 **Verification Queries**

Run these in SQL Editor to verify setup:

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('sync_operations', 'connection_status', 'sync_status_snapshots');

-- Check if functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('update_sync_operation_progress', 'complete_sync_operation', 'create_sync_status_snapshot');

-- Test creating a sync operation
INSERT INTO sync_operations (operation_type, source) 
VALUES ('upload', 'manual') 
RETURNING id;
```

## 🎉 **Success Indicators**

You'll know the setup worked when:
- ✅ No more "relation does not exist" errors
- ✅ Sync status indicators appear in the media page header
- ✅ Real-time progress tracking works during uploads
- ✅ System health dashboard shows data

## 🚀 **Next Steps**

After successful setup:
1. **Test file uploads** - Watch real-time progress tracking
2. **Monitor sync status** - Check the dashboard indicators
3. **Verify WebSocket connection** - Look for connection status
4. **Create manual snapshots** - Test system health monitoring

**Your enterprise-grade sync status management is now ready!** 🎯
