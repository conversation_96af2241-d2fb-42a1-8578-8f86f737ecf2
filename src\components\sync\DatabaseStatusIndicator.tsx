/**
 * Database Status Indicator - Phase 3 Schema Detection
 * 
 * Professional component to detect and display database schema status
 * with actionable guidance for users.
 * 
 * <AUTHOR> Project Team
 * @version 3.0.0
 */

'use client'

import React, { useState, useEffect } from 'react'
import { AlertTriangle, CheckCircle, Database, ExternalLink, RefreshCw } from 'lucide-react'
import { SyncStatusManager } from '@/lib/syncStatusManager'

interface DatabaseStatus {
  isReady: boolean
  isChecking: boolean
  lastChecked: Date | null
  error?: string
}

export function DatabaseStatusIndicator() {
  const [status, setStatus] = useState<DatabaseStatus>({
    isReady: false,
    isChecking: true,
    lastChecked: null
  })

  const checkDatabaseStatus = async () => {
    setStatus(prev => ({ ...prev, isChecking: true, error: undefined }))
    
    try {
      // Test if we can get active operations (this will check schema availability)
      await SyncStatusManager.getActiveSyncOperations()
      
      setStatus({
        isReady: true,
        isChecking: false,
        lastChecked: new Date()
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      const isSchemaError = errorMessage.includes('does not exist')
      
      setStatus({
        isReady: false,
        isChecking: false,
        lastChecked: new Date(),
        error: isSchemaError ? 'Phase 3 database schema not installed' : errorMessage
      })
    }
  }

  useEffect(() => {
    checkDatabaseStatus()
  }, [])

  if (status.isReady) {
    return (
      <div className="flex items-center space-x-2 px-3 py-2 bg-green-50 border border-green-200 rounded-lg">
        <CheckCircle className="h-4 w-4 text-green-600" />
        <span className="text-sm text-green-800 font-medium">Database Ready</span>
        <span className="text-xs text-green-600">Phase 3 Schema Active</span>
      </div>
    )
  }

  if (status.isChecking) {
    return (
      <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
        <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
        <span className="text-sm text-blue-800">Checking Database...</span>
      </div>
    )
  }

  return (
    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
      <div className="flex items-start space-x-3">
        <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-amber-800">Database Schema Update Required</h3>
          <p className="text-sm text-amber-700 mt-1">
            Phase 3 sync status management requires database migration.
          </p>
          
          <div className="mt-3 space-y-2">
            <div className="text-xs text-amber-600 bg-amber-100 p-2 rounded">
              <strong>Quick Fix:</strong>
              <ol className="list-decimal list-inside mt-1 space-y-1">
                <li>Open your Supabase dashboard</li>
                <li>Go to SQL Editor</li>
                <li>Run the schema from <code>supabase-schema.sql</code></li>
                <li>Refresh this page</li>
              </ol>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={checkDatabaseStatus}
                disabled={status.isChecking}
                className="inline-flex items-center px-3 py-1.5 bg-amber-600 text-white text-xs rounded hover:bg-amber-700 transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`h-3 w-3 mr-1 ${status.isChecking ? 'animate-spin' : ''}`} />
                Recheck Status
              </button>
              
              <a
                href="https://supabase.com"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-1.5 bg-gray-600 text-white text-xs rounded hover:bg-gray-700 transition-colors"
              >
                <Database className="h-3 w-3 mr-1" />
                Open Supabase
                <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
          </div>
          
          {status.error && (
            <div className="mt-2 text-xs text-amber-600 bg-amber-100 p-2 rounded">
              <strong>Error:</strong> {status.error}
            </div>
          )}
          
          {status.lastChecked && (
            <div className="mt-2 text-xs text-amber-600">
              Last checked: {status.lastChecked.toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default DatabaseStatusIndicator
